<div class="support-chat-container">
  <!-- Cha<PERSON> -->
  <div class="chat-header">
    <div class="flex items-center justify-between w-full">
      <div class="flex items-center space-x-4">
        <button
          (click)="onBackToRooms()"
          class="back-button">
          <fa-icon [icon]="['fas', 'arrow-left']"></fa-icon>
        </button>

        <div class="user-info">
          <div class="user-avatar">
            <fa-icon [icon]="['fas', 'user']" class="text-blue-600"></fa-icon>
          </div>
          <div class="user-details">
            <h3 class="user-name">{{ getUserDisplayName() }}</h3>
            <p class="user-email">{{ getUserEmail() }}</p>
          </div>
        </div>
      </div>

      <!-- Debug WebSocket Buttons -->
      <div class="flex gap-2">
        <button
          (click)="testWebSocketDetailed()"
          class="px-3 py-1 text-xs bg-blue-200 hover:bg-blue-300 rounded-md transition-colors"
          title="Test WebSocket Connection">
          <fa-icon [icon]="['fas', 'wifi']"></fa-icon>
          WS Test
        </button>
        <button
          (click)="forceReconnectWebSocket()"
          class="px-3 py-1 text-xs bg-orange-200 hover:bg-orange-300 rounded-md transition-colors"
          title="Force Reconnect WebSocket">
          <fa-icon [icon]="['fas', 'sync']"></fa-icon>
          Reconnect
        </button>
      </div>
    </div>
  </div>

  <!-- Messages Container -->
  <div class="chat-messages" #messagesContainer (scroll)="onScroll($event)">
    <div class="messages-list">
      <!-- Load more indicator -->
      <div class="load-more-indicator" *ngIf="isLoadingMore">
        <fa-icon [icon]="['fas', 'spinner']" [spin]="true"></fa-icon>
        <span>{{ 'chat.loading_more' | translate }}</span>
      </div>

      <!-- No more messages indicator -->
      <div class="no-more-messages" *ngIf="!hasMoreMessages && messages.length > 0">
        <span>{{ 'chat.no_more_messages' | translate }}</span>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading && messages.length === 0" class="loading-state">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="text-gray-600 mt-2">Loading messages...</p>
      </div>

      <!-- Messages -->
      <div *ngFor="let message of messages"
           class="message-item"
           [class.own-message]="(message.sender_id) === getCurrentUserId()"
           [class.user-message]="(message.sender_id) !== getCurrentUserId()">
        <div class="message-content">
          <!-- Image message -->
          <div *ngIf="isImageMessage(message)" class="message-image">
            <img [src]="message.file_url" [alt]="message.file_name" (click)="downloadFile(message.file_url!, message.file_name!)">
            <div class="message-text" *ngIf="message.content && message.content.trim()">{{ message.content }}</div>
          </div>

          <!-- File message -->
          <div *ngIf="isFileMessage(message)" class="message-file">
            <div class="file-info" (click)="downloadFile(message.file_url!, message.file_name!)">
              <fa-icon [icon]="['fas', getFileIcon(message.file_name!)]"></fa-icon>
              <div class="file-details">
                <div class="file-name">{{ message.file_name }}</div>
                <div class="file-size">{{ formatFileSize(message.file_size!) }}</div>
              </div>
              <fa-icon [icon]="['fas', 'download']"></fa-icon>
            </div>
            <div class="message-text" *ngIf="message.content && message.content.trim()">{{ message.content }}</div>
          </div>

          <!-- Text message -->
          <div *ngIf="!isImageMessage(message) && !isFileMessage(message)" class="message-text">{{ message.content }}</div>

          <div class="message-meta">
            <span class="message-time">
              {{ formatMessageTime(message.created_at || '') }}
            </span>
            <span *ngIf="message.sender?.full_name || message.sender?.user_name "
                  class="sender-name">
              - {{ message.sender?.full_name || message.sender?.user_name  }}
            </span>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="messages.length === 0 && !isLoading" class="empty-state">
        <fa-icon [icon]="['fas', 'comments']" class="text-gray-400 text-4xl mb-4"></fa-icon>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
        <p class="text-gray-600">Start the conversation by sending a reply.</p>
      </div>
    </div>
  </div>

  <!-- Message Input -->
  <div class="message-input-container">
    <!-- Selected file preview -->
    <div class="selected-file-preview" *ngIf="selectedFile">
      <div class="file-preview">
        <div class="file-info">
          <fa-icon [icon]="['fas', selectedFile.type.startsWith('image/') ? 'image' : 'file']"></fa-icon>
          <span class="file-name">{{ selectedFile.name }}</span>
          <span class="file-size">({{ formatFileSize(selectedFile.size) }})</span>
        </div>
        <button class="remove-file" (click)="removeSelectedFile()" type="button">
          <fa-icon [icon]="['fas', 'times']"></fa-icon>
        </button>
      </div>
    </div>

    <div class="message-input">
      <button class="attach-button" (click)="triggerFileInput()" type="button" [disabled]="isLoading || isUploadingFile">
        <fa-icon [icon]="['fas', 'paperclip']"></fa-icon>
      </button>

      <textarea #messageInput
                [(ngModel)]="newMessage"
                (keydown)="onKeyPress($event)"
                placeholder="Type your reply..."
                rows="1"
                class="input-field"></textarea>

      <button class="send-button"
              (click)="sendReply()"
              [disabled]="(!newMessage.trim() && !selectedFile) || isLoading || isUploadingFile">
        <fa-icon [icon]="['fas', 'paper-plane']" *ngIf="!isLoading && !isUploadingFile"></fa-icon>
        <fa-icon [icon]="['fas', 'spinner']" [spin]="true" *ngIf="isLoading || isUploadingFile"></fa-icon>
      </button>
    </div>

    <!-- Hidden file input -->
    <input #fileInput
           type="file"
           (change)="onFileSelected($event)"
           accept="image/*,.pdf,.doc,.docx,.txt"
           style="display: none;">
  </div>
</div>
