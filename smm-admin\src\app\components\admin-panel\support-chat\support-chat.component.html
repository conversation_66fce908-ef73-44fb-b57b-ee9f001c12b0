<div class="support-chat-container">
  <!-- Chat <PERSON> -->
  <div class="chat-header">
    <div class="flex items-center justify-between w-full">
      <div class="flex items-center space-x-4">
        <button
          (click)="onBackToRooms()"
          class="back-button">
          <fa-icon [icon]="['fas', 'arrow-left']"></fa-icon>
        </button>

        <div class="user-info">
          <div class="user-avatar">
            <fa-icon [icon]="['fas', 'user']" class="text-blue-600"></fa-icon>
          </div>
          <div class="user-details">
            <h3 class="user-name">{{ getUserDisplayName() }}</h3>
            <p class="user-email">{{ getUserEmail() }}</p>
          </div>
        </div>
      </div>

      <!-- Debug WebSocket Button -->
      <button
        (click)="testWebSocketConnection()"
        class="px-3 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
        title="Test WebSocket Connection">
        <fa-icon [icon]="['fas', 'wifi']"></fa-icon>
        WS Debug
      </button>
    </div>
  </div>

  <!-- Messages Container -->
  <div class="chat-messages" #messagesContainer>
    <div class="messages-list">
      <!-- Loading State -->
      <div *ngIf="isLoading && messages.length === 0" class="loading-state">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="text-gray-600 mt-2">Loading messages...</p>
      </div>

      <!-- Messages -->
      <div *ngFor="let message of messages"
           class="message-item"
           [class.own-message]="(message.sender_id) === getCurrentUserId()"
           [class.user-message]="(message.sender_id) !== getCurrentUserId()">
        <div class="message-content">
          <div class="message-text">{{ message.content }}</div>
          <div class="message-meta">
            <span class="message-time">
              {{ formatMessageTime(message.created_at || '') }}
            </span>
            <span *ngIf="message.sender?.full_name || message.sender?.user_name " 
                  class="sender-name">
              - {{ message.sender?.full_name || message.sender?.user_name  }}
            </span>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="messages.length === 0 && !isLoading" class="empty-state">
        <fa-icon [icon]="['fas', 'comments']" class="text-gray-400 text-4xl mb-4"></fa-icon>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
        <p class="text-gray-600">Start the conversation by sending a reply.</p>
      </div>
    </div>
  </div>

  <!-- Message Input -->
  <div class="message-input-container">
    <div class="message-input">
      <textarea #messageInput
                [(ngModel)]="newMessage"
                (keydown)="onKeyPress($event)"
                placeholder="Type your reply..."
                rows="1"
                class="input-field"></textarea>
      <button class="send-button"
              (click)="sendReply()"
              [disabled]="!newMessage.trim() || isLoading">
        <fa-icon [icon]="['fas', 'paper-plane']" *ngIf="!isLoading"></fa-icon>
        <fa-icon [icon]="['fas', 'spinner']" [spin]="true" *ngIf="isLoading"></fa-icon>
      </button>
    </div>
  </div>
</div>
