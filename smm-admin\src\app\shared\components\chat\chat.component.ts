import { Component, <PERSON><PERSON><PERSON>t, On<PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { ChatService, ChatRoom, ChatMessage, ChatMessageRequest } from '../../../core/services/chat.service';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';

import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { IconName } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss'],
  standalone: true,
  imports: [ CommonModule, TranslateModule, FormsModule, IconsModule ]
})
export class ChatComponent implements OnInit, OnDestroy {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;
  @ViewChild('fileInput') fileInput!: ElementRef;

  // Support chat properties
  supportMessages: ChatMessage[] = [];
  newMessage: string = '';
  isLoading = false;
  isChatOpen = false;
  currentUser: UserRes | undefined;
  supportChatRoom: ChatRoom | null = null;

  // Pagination properties
  currentPage = 0;
  hasMoreMessages = true;
  isLoadingMore = false;

  // File upload properties
  selectedFile: File | null = null;
  isUploadingFile = false;

  // Notification properties
  hasNewMessages = false;
  unreadCount = 0;
  lastSeenMessageId: number | null = null;
  isUserScrolledUp = false;

  // Audio notification
  private notificationSound: HTMLAudioElement | null = null;

  private subscriptions: Subscription[] = [];

  constructor(
    private chatService: ChatService,
    private userService: UserService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.initializeSupportChat();
    this.initializeNotificationSound();
  }

  private loadCurrentUser(): void {
    const userSub = this.userService.user$.subscribe(user => {
      this.currentUser = user;
    });
    this.subscriptions.push(userSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.chatService.disconnect();
  }

  private subscribeToSupportMessages(): void {
    if (!this.supportChatRoom) {
      console.error('Cannot subscribe to support messages: no support chat room');
      return;
    }

    console.log('=== Subscribing to Room Messages ===');
    console.log('Room ID:', this.supportChatRoom.id);
    console.log('Room details:', this.supportChatRoom);

    let isFirstLoad = true;

    // Subscribe to room-specific messages observable for real-time updates
    const roomMessagesSub = this.chatService.getRoomMessages$(this.supportChatRoom.id).subscribe(messages => {
      console.log('=== Chat Component Room Messages Update ===');
      console.log('Room ID:', this.supportChatRoom?.id);
      console.log('Messages count:', messages.length);
      console.log('Messages:', messages);
      console.log('Is first load:', isFirstLoad);

      const previousCount = this.supportMessages.length;
      this.supportMessages = messages;

      // Check for new messages (only after first load)
      if (messages.length > previousCount && previousCount > 0 && !isFirstLoad) {
        console.log('🔔 NEW MESSAGES DETECTED!');
        console.log('Previous count:', previousCount, 'New count:', messages.length);
        console.log('New messages:', messages.slice(previousCount));
        this.handleNewMessages(messages.slice(previousCount));
      }

      // Scroll to bottom logic
      if (isFirstLoad && messages.length > 0) {
        // Initial load - always scroll to bottom (latest message)
        console.log('Initial load: scrolling to bottom');
        setTimeout(() => this.forceScrollToBottom(), 200);
        isFirstLoad = false;
      } else if (!isFirstLoad) {
        // New messages - smart scroll only if user is near bottom
        console.log('New messages received - checking scroll position');
        console.log('Is user scrolled up:', this.isUserScrolledUp);

        if (!this.isUserScrolledUp) {
          console.log('User at bottom - auto scrolling');
          this.scrollToBottom();
        } else {
          console.log('User scrolled up - NOT auto scrolling, keeping indicator visible');
        }
      }
    });
    this.subscriptions.push(roomMessagesSub);
    console.log('Room messages subscription added');
  }

  private loadInitialSupportMessages(): void {
    if (!this.supportChatRoom) {
      console.error('Cannot load initial support messages: no support chat room');
      return;
    }

    console.log('=== Loading Initial Support Messages ===');
    console.log('Room ID:', this.supportChatRoom.id);

    this.isLoading = true;
    this.chatService.loadRoomMessages(this.supportChatRoom.id, 0, 50).subscribe({
      next: (response) => {
        console.log('=== Initial Support Messages Loaded ===');
        console.log('Response:', response);
        console.log('Messages count:', response.content?.length || 0);
        this.currentPage = 0;
        this.hasMoreMessages = response.content && response.content.length === 50;
        this.isLoading = false;

        // Force scroll to bottom after loading (initial load)
        setTimeout(() => {
          console.log('Initial load complete - forcing scroll to bottom');
          this.forceScrollToBottom();
        }, 300);
      },
      error: (error) => {
        console.error('Error loading initial support messages:', error);
        this.isLoading = false;
      }
    });
  }

  private initializeSupportChat(): void {
    // For panel users, get or create their support chat room
    this.chatService.getOrCreateSupportChatRoom().subscribe({
      next: (response) => {
        console.log('Support chat room response:', response);
        this.supportChatRoom = response;
        this.subscribeToSupportMessages();
        this.loadInitialSupportMessages();
      },
      error: (error) => {
        console.error('Error getting support chat room:', error);
        // Fallback: try to subscribe to global support messages
        this.subscribeToGlobalSupportMessages();
        this.loadGlobalSupportMessages();
      }
    });
  }

  // Fallback methods for when room-based approach fails
  private subscribeToGlobalSupportMessages(): void {
    console.log('Using fallback: subscribing to global support messages');
    // Subscribe to support messages observable for real-time updates
    const supportMessagesSub = this.chatService.supportMessages$.subscribe(messages => {
      console.log('Chat component received global support messages update:', messages.length);
      this.supportMessages = messages;
      this.scrollToBottom();
    });
    this.subscriptions.push(supportMessagesSub);
  }

  private loadGlobalSupportMessages(): void {
    console.log('Using fallback: loading global support messages');
    if (!this.currentUser) return;

    this.isLoading = true;
    this.chatService.loadSupportMessages().subscribe({
      next: (response) => {
        console.log('Global support messages loaded:', response);
        this.currentPage = 0;
        this.hasMoreMessages = false; // Global approach doesn't support pagination
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading global support messages:', error);
        this.isLoading = false;
      }
    });
  }

  loadSupportMessages(): void {
    // This method is now mainly for manual refresh if needed
    // Real-time updates are handled by subscribeToSupportMessages()
    if (this.supportChatRoom) {
      // If we have a room, use room-based loading
      this.loadInitialSupportMessages();
    } else {
      // Otherwise use global loading
      this.loadGlobalSupportMessages();
    }
  }

  // Add message to local state immediately for better UX
  private addMessageLocally(content: string, messageType: 'TEXT' | 'IMAGE' | 'FILE' = 'TEXT', fileUrl?: string, fileName?: string, fileSize?: number): void {
    if (!this.currentUser) return;

    const tempMessage: ChatMessage = {
      id: Date.now(), // Temporary ID
      content: content,
      message_type: messageType,
      sender_id: this.currentUser.id,
      chat_room_id: this.supportChatRoom?.id || 0,
      created_at: new Date().toISOString(),
      sender: {
        id: this.currentUser.id,
        user_name: this.currentUser.user_name,
        full_name: this.currentUser.user_name || this.currentUser.email || 'User' // Fallback for full_name
      },
      file_url: fileUrl,
      file_name: fileName,
      file_size: fileSize
    };

    console.log('Adding message locally:', tempMessage);

    // Add to local messages array immediately
    this.supportMessages = [...this.supportMessages, tempMessage];

    // Force scroll to bottom when user sends message
    setTimeout(() => {
      this.forceScrollToBottom();
      // Only clear notifications if they were for messages from others
      // Don't clear notifications for messages user is about to send
    }, 50);
  }

  // Conditional refresh - only refresh if WebSocket is not working properly
  private conditionalRefresh(): void {
    const wsInfo = this.chatService.getWebSocketInfo();
    console.log('=== Conditional Refresh Check ===');
    console.log('WebSocket connected:', wsInfo.connected);

    if (!wsInfo.connected) {
      console.log('WebSocket not connected, forcing refresh...');
      setTimeout(() => this.refreshMessages(), 500);
    } else {
      console.log('WebSocket connected, relying on real-time updates...');
      // WebSocket should handle real-time updates, but add a small delay just in case
      setTimeout(() => {
        // Only refresh if we don't see the message after a reasonable delay
        const currentCount = this.supportMessages.length;
        setTimeout(() => {
          if (this.supportMessages.length === currentCount) {
            console.log('Message not received via WebSocket, forcing refresh...');
            this.refreshMessages();
          }
        }, 2000);
      }, 100);
    }
  }

  // Force refresh messages after sending
  private refreshMessages(): void {
    console.log('=== Refreshing Messages After Send ===');

    if (this.supportChatRoom) {
      // Reload room messages to get the latest including the one just sent
      this.chatService.loadRoomMessages(this.supportChatRoom.id, 0, 50).subscribe({
        next: (response) => {
          console.log('Messages refreshed after send:', response.content?.length || 0);
          // The room messages observable will automatically update the UI
          setTimeout(() => this.scrollToBottom(), 100);
        },
        error: (error) => {
          console.error('Error refreshing messages after send:', error);
        }
      });
    } else {
      // Fallback to global refresh
      this.loadGlobalSupportMessages();
    }
  }

  sendSupportMessage(): void {
    if (!this.newMessage.trim() && !this.selectedFile) {
      return;
    }

    console.log('=== Sending Support Message ===');
    console.log('Message content:', this.newMessage.trim());
    console.log('Support chat room:', this.supportChatRoom);
    console.log('Current user:', this.currentUser);
    console.log('Selected file:', this.selectedFile);

    this.isLoading = true;

    if (this.selectedFile) {
      this.sendFileMessage();
    } else {
      this.sendTextMessage();
    }
  }

  private sendTextMessage(): void {
    const messageContent = this.newMessage.trim();
    const messageRequest: ChatMessageRequest = {
      chat_room_id: this.supportChatRoom?.id || 0,
      content: messageContent,
      message_type: 'TEXT'
    };

    console.log('Text message request:', messageRequest);

    // Add message locally immediately for better UX
    this.addMessageLocally(messageContent, 'TEXT');

    const sub = this.chatService.sendSupportMessage(messageRequest).subscribe({
      next: (response) => {
        console.log('Support message sent successfully:', response);
        this.newMessage = '';
        this.isLoading = false;

        // Refresh to get the real message from server if WebSocket is not working properly
        this.conditionalRefresh();
      },
      error: (error) => {
        console.error('Error sending support message:', error);
        this.isLoading = false;
        // TODO: Remove the temp message or mark it as failed
      }
    });
    this.subscriptions.push(sub);
  }

  private sendFileMessage(): void {
    if (!this.selectedFile) return;

    // Since we only allow images now, always use IMAGE type
    const messageContent = this.newMessage.trim() || 'Sent an image';
    const messageType: 'IMAGE' = 'IMAGE';
    const fileName = this.selectedFile.name;
    const fileSize = this.selectedFile.size;

    this.isUploadingFile = true;

    // Add message locally immediately for better UX (with placeholder for image)
    this.addMessageLocally(messageContent, messageType, undefined, fileName, fileSize);

    // First upload the file
    this.chatService.uploadFile(this.selectedFile).subscribe({
      next: (uploadResponse: any) => {
        console.log('Image uploaded successfully:', uploadResponse);

        // Then send message with image info
        const messageRequest: ChatMessageRequest = {
          chat_room_id: this.supportChatRoom?.id || 0,
          content: messageContent,
          message_type: messageType,
          file_url: uploadResponse.url, // Use 'url' from response
          file_name: fileName,
          file_size: fileSize
        };

        const sub = this.chatService.sendSupportMessage(messageRequest).subscribe({
          next: (response) => {
            console.log('Image message sent successfully:', response);
            this.newMessage = '';
            this.selectedFile = null;
            this.isLoading = false;
            this.isUploadingFile = false;

            // Refresh to get the real message from server if WebSocket is not working properly
            this.conditionalRefresh();
          },
          error: (error) => {
            console.error('Error sending image message:', error);
            this.isLoading = false;
            this.isUploadingFile = false;
          }
        });
        this.subscriptions.push(sub);
      },
      error: (error: any) => {
        console.error('Error uploading image:', error);
        this.isLoading = false;
        this.isUploadingFile = false;
        // TODO: Remove the temp message or mark it as failed
      }
    });
  }

  toggleChat(): void {
    this.isChatOpen = !this.isChatOpen;
    if (this.isChatOpen) {
      this.loadSupportMessages();
      // Reset scroll tracking and clear notifications when opening chat
      this.isUserScrolledUp = false;
      setTimeout(() => {
        this.markMessagesAsRead();
        this.forceScrollToBottom();
      }, 100);
    } else {
      // Clear notifications when closing chat
      console.log('🔒 Chat closed - clearing all notifications');
      this.markMessagesAsRead();
    }
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendSupportMessage();
    }
  }

  private scrollToBottom(force: boolean = false): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;

        // Check if user is near bottom (within 100px) or force scroll
        const isNearBottom = element.scrollHeight - element.scrollTop - element.clientHeight < 100;

        if (force || isNearBottom) {
          // Smooth scroll to bottom
          element.scrollTo({
            top: element.scrollHeight,
            behavior: 'smooth'
          });
        }
      }
    }, 100);
  }

  // Force scroll to bottom (used when sending messages)
  private forceScrollToBottom(): void {
    console.log('🚀 Force scrolling to bottom...');
    if (this.messagesContainer) {
      const element = this.messagesContainer.nativeElement;
      console.log('Before force scroll:');
      console.log('- scrollTop:', element.scrollTop);
      console.log('- scrollHeight:', element.scrollHeight);
      console.log('- clientHeight:', element.clientHeight);

      // Try multiple methods to ensure scroll works
      element.scrollTop = element.scrollHeight;

      // Also try scrollTo method
      element.scrollTo({
        top: element.scrollHeight,
        behavior: 'auto' // Use 'auto' instead of 'smooth' for immediate scroll
      });

      console.log('After force scroll:');
      console.log('- scrollTop:', element.scrollTop);
      console.log('- scrollHeight:', element.scrollHeight);

      // Update scroll tracking
      this.isUserScrolledUp = false;
      console.log('✅ Force scroll completed - isUserScrolledUp:', this.isUserScrolledUp);
    } else {
      console.error('❌ Messages container not found for force scroll');
    }
  }

  getRoomDisplayName(room: ChatRoom): string {
    if (room.type === 'DIRECT' && room.participants) {
      // For direct chats, show the other participant's name
      const otherParticipant = room.participants.find(p => p.user_id !== this.getCurrentUserId());
      return otherParticipant?.user?.user_name || room.name || 'Direct Chat';
    }
    return room.name || 'Group Chat';
  }

  getCurrentUserId(): number {
    return this.currentUser?.id || 0;
  }

  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getTotalUnreadCount(): number {
    // For support chat, we can return 0 for now
    // Later we can implement unread count for support messages
    return 0;
  }

  // File handling methods
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check file type - only images allowed
      if (!file.type.startsWith('image/')) {
        this.translateService.get('chat.only_images_allowed').subscribe(message => {
          alert(message || 'Only image files are allowed');
        });
        return;
      }

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        this.translateService.get('chat.file_too_large').subscribe(message => {
          alert(message || 'File size must be less than 10MB');
        });
        return;
      }

      this.selectedFile = file;
      console.log('Image selected:', file);
    }
  }

  removeSelectedFile(): void {
    this.selectedFile = null;
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  // Scroll handling for load more and notification tracking
  onScroll(event: any): void {
    const element = event.target;

    // Load more messages when scrolled to top
    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {
      this.loadMoreMessages();
    }

    // Track if user is scrolled up (not at bottom)
    const isAtBottom = element.scrollHeight - element.scrollTop - element.clientHeight < 50;
    const wasScrolledUp = this.isUserScrolledUp;
    this.isUserScrolledUp = !isAtBottom;

    // Debug scroll position with more details
    if (wasScrolledUp !== this.isUserScrolledUp) {
      console.log('📍 === SCROLL POSITION CHANGED ===');
      console.log('- scrollTop:', element.scrollTop);
      console.log('- scrollHeight:', element.scrollHeight);
      console.log('- clientHeight:', element.clientHeight);
      console.log('- Distance from bottom:', element.scrollHeight - element.scrollTop - element.clientHeight);
      console.log('- isAtBottom:', isAtBottom);
      console.log('- isUserScrolledUp:', this.isUserScrolledUp);
      console.log('- hasNewMessages:', this.hasNewMessages);
      console.log('- Should show indicator:', this.hasNewMessages && this.isUserScrolledUp);
    }

    // Clear notifications if user scrolled to bottom
    if (isAtBottom && this.hasNewMessages) {
      console.log('✅ User scrolled to bottom - clearing notifications');
      this.markMessagesAsRead();
    }
  }

  private loadMoreMessages(): void {
    if (!this.supportChatRoom || this.isLoadingMore || !this.hasMoreMessages) return;

    this.isLoadingMore = true;
    const nextPage = this.currentPage + 1;

    this.chatService.loadRoomMessages(this.supportChatRoom.id, nextPage, 50).subscribe({
      next: (response) => {
        console.log(`Loaded page ${nextPage} messages:`, response);

        if (response.content && response.content.length > 0) {
          this.currentPage = nextPage;
          this.hasMoreMessages = response.content.length === 50;
        } else {
          this.hasMoreMessages = false;
        }

        this.isLoadingMore = false;
      },
      error: (error) => {
        console.error('Error loading more messages:', error);
        this.isLoadingMore = false;
      }
    });
  }

  // Message type checking
  isImageMessage(message: ChatMessage): boolean {
    return message.message_type === 'IMAGE' && !!message.file_url;
  }

  isFileMessage(message: ChatMessage): boolean {
    return message.message_type === 'FILE' && !!message.file_url;
  }

  getFileIcon(fileName: string): IconName {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return 'file-pdf';
      case 'doc':
      case 'docx': return 'file-word';
      case 'xls':
      case 'xlsx': return 'file-excel';
      case 'ppt':
      case 'pptx': return 'file-powerpoint';
      case 'txt': return 'file-alt';
      default: return 'file';
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  downloadFile(fileUrl: string, fileName: string): void {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  // Get image preview URL for selected file
  getImagePreview(file: File): string {
    return URL.createObjectURL(file);
  }

  // Initialize notification sound
  private initializeNotificationSound(): void {
    try {
      // Create notification sound (you can replace with your own sound file)
      this.notificationSound = new Audio();
      this.notificationSound.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
      this.notificationSound.volume = 0.5;
      this.notificationSound.load();
    } catch (error) {
      console.warn('Could not initialize notification sound:', error);
    }
  }

  // Handle new messages (notifications, sound, etc.)
  private handleNewMessages(newMessages: ChatMessage[]): void {
    console.log('🔔 === HANDLE NEW MESSAGES ===');
    console.log('New messages received:', newMessages.length);
    console.log('Current user ID:', this.currentUser?.id);

    if (!newMessages || newMessages.length === 0) {
      console.log('❌ No new messages to handle');
      return;
    }

    // Check if any new message is from another user (not current user)
    const messagesFromOthers = newMessages.filter(msg => {
      console.log(`Message from sender ${msg.sender_id}, current user: ${this.currentUser?.id}`);
      return msg.sender_id !== this.currentUser?.id;
    });

    console.log('Messages from others:', messagesFromOthers.length);

    if (messagesFromOthers.length > 0) {
      console.log('📢 Processing messages from others:', messagesFromOthers.length);
      console.log('Current state before update:');
      console.log('- Is user scrolled up:', this.isUserScrolledUp);
      console.log('- Is chat focused:', this.isChatFocused());
      console.log('- Current unread count:', this.unreadCount);
      console.log('- Has new messages:', this.hasNewMessages);

      // Always update unread count for messages from others
      this.unreadCount += messagesFromOthers.length;
      this.hasNewMessages = true;

      console.log('📊 Updated state:');
      console.log('- Updated unread count:', this.unreadCount);
      console.log('- Has new messages:', this.hasNewMessages);
      console.log('- Should show indicator:', this.hasNewMessages && this.isUserScrolledUp);

      // Play notification sound if chat is not focused or user is scrolled up
      if (!this.isChatFocused() || this.isUserScrolledUp) {
        console.log('🔊 Playing notification sound');
        this.playNotificationSound();
      }

      // Show browser notification if supported and chat is not focused
      if (!this.isChatFocused()) {
        console.log('🖥️ Showing browser notification');
        this.showBrowserNotification(messagesFromOthers[0]);
      }
    } else {
      console.log('ℹ️ All new messages are from current user - no notifications needed');
    }
  }

  // Check if chat is currently focused/visible
  private isChatFocused(): boolean {
    return document.hasFocus() && this.isChatOpen;
  }

  // Play notification sound
  private playNotificationSound(): void {
    try {
      if (this.notificationSound) {
        this.notificationSound.currentTime = 0;
        this.notificationSound.play().catch(error => {
          console.warn('Could not play notification sound:', error);
        });
      }
    } catch (error) {
      console.warn('Error playing notification sound:', error);
    }
  }

  // Show browser notification
  private showBrowserNotification(message: ChatMessage): void {
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        const senderName = message.sender?.full_name || message.sender?.user_name || 'Someone';
        const content = message.message_type === 'IMAGE' ? 'Sent an image' : message.content;

        const notification = new Notification(`New message from ${senderName}`, {
          body: content,
          icon: '/assets/icons/chat-icon.png', // Add your chat icon
          tag: 'chat-message',
          requireInteraction: false
        });

        // Auto close after 5 seconds
        setTimeout(() => notification.close(), 5000);

        // Click to focus chat
        notification.onclick = () => {
          window.focus();
          this.focusChat();
          notification.close();
        };
      } else if (Notification.permission !== 'denied') {
        // Request permission
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            this.showBrowserNotification(message);
          }
        });
      }
    }
  }

  // Focus chat and clear notifications
  private focusChat(): void {
    if (!this.isChatOpen) {
      this.toggleChat();
    }
    this.markMessagesAsRead();
    setTimeout(() => this.forceScrollToBottom(), 100);
  }

  // Mark messages as read and clear notifications
  private markMessagesAsRead(): void {
    this.hasNewMessages = false;
    this.unreadCount = 0;

    // Update last seen message ID
    if (this.supportMessages.length > 0) {
      this.lastSeenMessageId = this.supportMessages[this.supportMessages.length - 1].id;
    }
  }

  // Scroll to new messages when clicking indicator
  scrollToNewMessages(): void {
    console.log('🚀 === CLICKING NEW MESSAGES INDICATOR ===');
    console.log('Current unread count:', this.unreadCount);
    console.log('Has new messages:', this.hasNewMessages);
    console.log('Is user scrolled up:', this.isUserScrolledUp);
    console.log('Messages container:', this.messagesContainer);

    if (this.messagesContainer) {
      const element = this.messagesContainer.nativeElement;
      console.log('Before scroll - scrollTop:', element.scrollTop, 'scrollHeight:', element.scrollHeight);

      // Try multiple scroll methods
      element.scrollTop = element.scrollHeight;

      // Also try scrollTo
      element.scrollTo({
        top: element.scrollHeight,
        behavior: 'auto'
      });

      // Force with setTimeout
      setTimeout(() => {
        element.scrollTop = element.scrollHeight;
        console.log('After timeout scroll - scrollTop:', element.scrollTop, 'scrollHeight:', element.scrollHeight);
      }, 10);

      console.log('After immediate scroll - scrollTop:', element.scrollTop, 'scrollHeight:', element.scrollHeight);

      // Update state immediately
      this.isUserScrolledUp = false;
      this.markMessagesAsRead();

      console.log('✅ Scroll completed and notifications cleared');
      console.log('Final state - isUserScrolledUp:', this.isUserScrolledUp, 'hasNewMessages:', this.hasNewMessages);
    } else {
      console.error('❌ Messages container not found!');
    }
  }














}
