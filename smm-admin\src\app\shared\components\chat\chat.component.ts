import { Component, <PERSON><PERSON><PERSON>t, On<PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { ChatService, ChatRoom, ChatMessage, ChatMessageRequest } from '../../../core/services/chat.service';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';

import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { IconName } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss'],
  standalone: true,
  imports: [ CommonModule, TranslateModule, FormsModule, IconsModule ]
})
export class ChatComponent implements OnInit, OnDestroy {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;
  @ViewChild('fileInput') fileInput!: ElementRef;

  // Support chat properties
  supportMessages: ChatMessage[] = [];
  newMessage: string = '';
  isLoading = false;
  isChatOpen = false;
  currentUser: UserRes | undefined;
  supportChatRoom: ChatRoom | null = null;

  // Pagination properties
  currentPage = 0;
  hasMoreMessages = true;
  isLoadingMore = false;

  // File upload properties
  selectedFile: File | null = null;
  isUploadingFile = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private chatService: ChatService,
    private userService: UserService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.initializeSupportChat();
  }

  private loadCurrentUser(): void {
    const userSub = this.userService.user$.subscribe(user => {
      this.currentUser = user;
    });
    this.subscriptions.push(userSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.chatService.disconnect();
  }

  private subscribeToSupportMessages(): void {
    if (!this.supportChatRoom) {
      console.error('Cannot subscribe to support messages: no support chat room');
      return;
    }

    console.log('=== Subscribing to Room Messages ===');
    console.log('Room ID:', this.supportChatRoom.id);
    console.log('Room details:', this.supportChatRoom);

    // Subscribe to room-specific messages observable for real-time updates
    const roomMessagesSub = this.chatService.getRoomMessages$(this.supportChatRoom.id).subscribe(messages => {
      console.log('=== Chat Component Room Messages Update ===');
      console.log('Room ID:', this.supportChatRoom?.id);
      console.log('Messages count:', messages.length);
      console.log('Messages:', messages);
      this.supportMessages = messages;
      this.scrollToBottom();
    });
    this.subscriptions.push(roomMessagesSub);
    console.log('Room messages subscription added');
  }

  private loadInitialSupportMessages(): void {
    if (!this.supportChatRoom) {
      console.error('Cannot load initial support messages: no support chat room');
      return;
    }

    console.log('=== Loading Initial Support Messages ===');
    console.log('Room ID:', this.supportChatRoom.id);

    this.isLoading = true;
    this.chatService.loadRoomMessages(this.supportChatRoom.id, 0, 50).subscribe({
      next: (response) => {
        console.log('=== Initial Support Messages Loaded ===');
        console.log('Response:', response);
        console.log('Messages count:', response.content?.length || 0);
        this.currentPage = 0;
        this.hasMoreMessages = response.content && response.content.length === 50;
        this.isLoading = false;

        // Force scroll to bottom after loading
        setTimeout(() => this.scrollToBottom(), 100);
      },
      error: (error) => {
        console.error('Error loading initial support messages:', error);
        this.isLoading = false;
      }
    });
  }

  private initializeSupportChat(): void {
    // For panel users, get or create their support chat room
    this.chatService.getOrCreateSupportChatRoom().subscribe({
      next: (response) => {
        console.log('Support chat room response:', response);
        this.supportChatRoom = response;
        this.subscribeToSupportMessages();
        this.loadInitialSupportMessages();
      },
      error: (error) => {
        console.error('Error getting support chat room:', error);
        // Fallback: try to subscribe to global support messages
        this.subscribeToGlobalSupportMessages();
        this.loadGlobalSupportMessages();
      }
    });
  }

  // Fallback methods for when room-based approach fails
  private subscribeToGlobalSupportMessages(): void {
    console.log('Using fallback: subscribing to global support messages');
    // Subscribe to support messages observable for real-time updates
    const supportMessagesSub = this.chatService.supportMessages$.subscribe(messages => {
      console.log('Chat component received global support messages update:', messages.length);
      this.supportMessages = messages;
      this.scrollToBottom();
    });
    this.subscriptions.push(supportMessagesSub);
  }

  private loadGlobalSupportMessages(): void {
    console.log('Using fallback: loading global support messages');
    if (!this.currentUser) return;

    this.isLoading = true;
    this.chatService.loadSupportMessages().subscribe({
      next: (response) => {
        console.log('Global support messages loaded:', response);
        this.currentPage = 0;
        this.hasMoreMessages = false; // Global approach doesn't support pagination
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading global support messages:', error);
        this.isLoading = false;
      }
    });
  }

  loadSupportMessages(): void {
    // This method is now mainly for manual refresh if needed
    // Real-time updates are handled by subscribeToSupportMessages()
    if (this.supportChatRoom) {
      // If we have a room, use room-based loading
      this.loadInitialSupportMessages();
    } else {
      // Otherwise use global loading
      this.loadGlobalSupportMessages();
    }
  }

  // Add message to local state immediately for better UX
  private addMessageLocally(content: string, messageType: 'TEXT' | 'IMAGE' | 'FILE' = 'TEXT', fileUrl?: string, fileName?: string, fileSize?: number): void {
    if (!this.currentUser) return;

    const tempMessage: ChatMessage = {
      id: Date.now(), // Temporary ID
      content: content,
      message_type: messageType,
      sender_id: this.currentUser.id,
      chat_room_id: this.supportChatRoom?.id || 0,
      created_at: new Date().toISOString(),
      sender: {
        id: this.currentUser.id,
        user_name: this.currentUser.user_name,
        full_name: this.currentUser.user_name || this.currentUser.email || 'User' // Fallback for full_name
      },
      file_url: fileUrl,
      file_name: fileName,
      file_size: fileSize
    };

    console.log('Adding message locally:', tempMessage);

    // Add to local messages array immediately
    this.supportMessages = [...this.supportMessages, tempMessage];
    setTimeout(() => this.forceScrollToBottom(), 50);
  }

  // Conditional refresh - only refresh if WebSocket is not working properly
  private conditionalRefresh(): void {
    const wsInfo = this.chatService.getWebSocketInfo();
    console.log('=== Conditional Refresh Check ===');
    console.log('WebSocket connected:', wsInfo.connected);

    if (!wsInfo.connected) {
      console.log('WebSocket not connected, forcing refresh...');
      setTimeout(() => this.refreshMessages(), 500);
    } else {
      console.log('WebSocket connected, relying on real-time updates...');
      // WebSocket should handle real-time updates, but add a small delay just in case
      setTimeout(() => {
        // Only refresh if we don't see the message after a reasonable delay
        const currentCount = this.supportMessages.length;
        setTimeout(() => {
          if (this.supportMessages.length === currentCount) {
            console.log('Message not received via WebSocket, forcing refresh...');
            this.refreshMessages();
          }
        }, 2000);
      }, 100);
    }
  }

  // Force refresh messages after sending
  private refreshMessages(): void {
    console.log('=== Refreshing Messages After Send ===');

    if (this.supportChatRoom) {
      // Reload room messages to get the latest including the one just sent
      this.chatService.loadRoomMessages(this.supportChatRoom.id, 0, 50).subscribe({
        next: (response) => {
          console.log('Messages refreshed after send:', response.content?.length || 0);
          // The room messages observable will automatically update the UI
          setTimeout(() => this.scrollToBottom(), 100);
        },
        error: (error) => {
          console.error('Error refreshing messages after send:', error);
        }
      });
    } else {
      // Fallback to global refresh
      this.loadGlobalSupportMessages();
    }
  }

  sendSupportMessage(): void {
    if (!this.newMessage.trim() && !this.selectedFile) {
      return;
    }

    console.log('=== Sending Support Message ===');
    console.log('Message content:', this.newMessage.trim());
    console.log('Support chat room:', this.supportChatRoom);
    console.log('Current user:', this.currentUser);
    console.log('Selected file:', this.selectedFile);

    this.isLoading = true;

    if (this.selectedFile) {
      this.sendFileMessage();
    } else {
      this.sendTextMessage();
    }
  }

  private sendTextMessage(): void {
    const messageContent = this.newMessage.trim();
    const messageRequest: ChatMessageRequest = {
      chat_room_id: this.supportChatRoom?.id || 0,
      content: messageContent,
      message_type: 'TEXT'
    };

    console.log('Text message request:', messageRequest);

    // Add message locally immediately for better UX
    this.addMessageLocally(messageContent, 'TEXT');

    const sub = this.chatService.sendSupportMessage(messageRequest).subscribe({
      next: (response) => {
        console.log('Support message sent successfully:', response);
        this.newMessage = '';
        this.isLoading = false;

        // Refresh to get the real message from server if WebSocket is not working properly
        this.conditionalRefresh();
      },
      error: (error) => {
        console.error('Error sending support message:', error);
        this.isLoading = false;
        // TODO: Remove the temp message or mark it as failed
      }
    });
    this.subscriptions.push(sub);
  }

  private sendFileMessage(): void {
    if (!this.selectedFile) return;

    // Since we only allow images now, always use IMAGE type
    const messageContent = this.newMessage.trim() || 'Sent an image';
    const messageType: 'IMAGE' = 'IMAGE';
    const fileName = this.selectedFile.name;
    const fileSize = this.selectedFile.size;

    this.isUploadingFile = true;

    // Add message locally immediately for better UX (with placeholder for image)
    this.addMessageLocally(messageContent, messageType, undefined, fileName, fileSize);

    // First upload the file
    this.chatService.uploadFile(this.selectedFile).subscribe({
      next: (uploadResponse: any) => {
        console.log('Image uploaded successfully:', uploadResponse);

        // Then send message with image info
        const messageRequest: ChatMessageRequest = {
          chat_room_id: this.supportChatRoom?.id || 0,
          content: messageContent,
          message_type: messageType,
          file_url: uploadResponse.url, // Use 'url' from response
          file_name: fileName,
          file_size: fileSize
        };

        const sub = this.chatService.sendSupportMessage(messageRequest).subscribe({
          next: (response) => {
            console.log('Image message sent successfully:', response);
            this.newMessage = '';
            this.selectedFile = null;
            this.isLoading = false;
            this.isUploadingFile = false;

            // Refresh to get the real message from server if WebSocket is not working properly
            this.conditionalRefresh();
          },
          error: (error) => {
            console.error('Error sending image message:', error);
            this.isLoading = false;
            this.isUploadingFile = false;
          }
        });
        this.subscriptions.push(sub);
      },
      error: (error: any) => {
        console.error('Error uploading image:', error);
        this.isLoading = false;
        this.isUploadingFile = false;
        // TODO: Remove the temp message or mark it as failed
      }
    });
  }

  toggleChat(): void {
    this.isChatOpen = !this.isChatOpen;
    if (this.isChatOpen) {
      this.loadSupportMessages();
    }
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendSupportMessage();
    }
  }

  private scrollToBottom(force: boolean = false): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;

        // Check if user is near bottom (within 100px) or force scroll
        const isNearBottom = element.scrollHeight - element.scrollTop - element.clientHeight < 100;

        if (force || isNearBottom) {
          // Smooth scroll to bottom
          element.scrollTo({
            top: element.scrollHeight,
            behavior: 'smooth'
          });
        }
      }
    }, 100);
  }

  // Force scroll to bottom (used when sending messages)
  private forceScrollToBottom(): void {
    this.scrollToBottom(true);
  }

  getRoomDisplayName(room: ChatRoom): string {
    if (room.type === 'DIRECT' && room.participants) {
      // For direct chats, show the other participant's name
      const otherParticipant = room.participants.find(p => p.user_id !== this.getCurrentUserId());
      return otherParticipant?.user?.user_name || room.name || 'Direct Chat';
    }
    return room.name || 'Group Chat';
  }

  getCurrentUserId(): number {
    return this.currentUser?.id || 0;
  }

  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getTotalUnreadCount(): number {
    // For support chat, we can return 0 for now
    // Later we can implement unread count for support messages
    return 0;
  }

  // File handling methods
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check file type - only images allowed
      if (!file.type.startsWith('image/')) {
        this.translateService.get('chat.only_images_allowed').subscribe(message => {
          alert(message || 'Only image files are allowed');
        });
        return;
      }

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        this.translateService.get('chat.file_too_large').subscribe(message => {
          alert(message || 'File size must be less than 10MB');
        });
        return;
      }

      this.selectedFile = file;
      console.log('Image selected:', file);
    }
  }

  removeSelectedFile(): void {
    this.selectedFile = null;
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  // Scroll handling for load more
  onScroll(event: any): void {
    const element = event.target;
    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {
      this.loadMoreMessages();
    }
  }

  private loadMoreMessages(): void {
    if (!this.supportChatRoom || this.isLoadingMore || !this.hasMoreMessages) return;

    this.isLoadingMore = true;
    const nextPage = this.currentPage + 1;

    this.chatService.loadRoomMessages(this.supportChatRoom.id, nextPage, 50).subscribe({
      next: (response) => {
        console.log(`Loaded page ${nextPage} messages:`, response);

        if (response.content && response.content.length > 0) {
          this.currentPage = nextPage;
          this.hasMoreMessages = response.content.length === 50;
        } else {
          this.hasMoreMessages = false;
        }

        this.isLoadingMore = false;
      },
      error: (error) => {
        console.error('Error loading more messages:', error);
        this.isLoadingMore = false;
      }
    });
  }

  // Message type checking
  isImageMessage(message: ChatMessage): boolean {
    return message.message_type === 'IMAGE' && !!message.file_url;
  }

  isFileMessage(message: ChatMessage): boolean {
    return message.message_type === 'FILE' && !!message.file_url;
  }

  getFileIcon(fileName: string): IconName {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return 'file-pdf';
      case 'doc':
      case 'docx': return 'file-word';
      case 'xls':
      case 'xlsx': return 'file-excel';
      case 'ppt':
      case 'pptx': return 'file-powerpoint';
      case 'txt': return 'file-alt';
      default: return 'file';
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  downloadFile(fileUrl: string, fileName: string): void {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  // Get image preview URL for selected file
  getImagePreview(file: File): string {
    return URL.createObjectURL(file);
  }

  // Debug methods
  testWebSocketConnection(): void {
    console.log('=== Testing WebSocket Connection (Chat Component) ===');
    const wsInfo = this.chatService.getWebSocketInfo();
    console.log('WebSocket Info:', wsInfo);

    if (!wsInfo.connected) {
      console.log('WebSocket not connected, attempting reconnection...');
      this.chatService.reconnectWebSocket();
    } else {
      console.log('WebSocket is connected successfully!');
      console.log('Current support messages count:', this.supportMessages.length);
      console.log('Support chat room:', this.supportChatRoom);
    }
  }

  forceReconnectWebSocket(): void {
    console.log('=== Force Reconnecting WebSocket (Chat Component) ===');
    this.chatService.forceReconnectWebSocket();

    // Wait a moment then reinitialize
    setTimeout(() => {
      this.initializeSupportChat();
    }, 2000);
  }

  testRoomSubscription(): void {
    console.log('=== Testing Room Subscription ===');
    if (this.supportChatRoom) {
      console.log('Current room:', this.supportChatRoom);
      console.log('Current messages:', this.supportMessages.length);

      // Test by resubscribing
      this.subscribeToSupportMessages();
      this.loadInitialSupportMessages();
    } else {
      console.log('No support chat room available');
    }
  }
}
