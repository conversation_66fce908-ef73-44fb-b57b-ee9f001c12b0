.support-chat-container {
  display: flex;
  flex-direction: column;
  height: 600px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chat-header {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;

  .back-button {
    padding: 8px;
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background: #e9ecef;
      color: #495057;
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .user-avatar {
      width: 40px;
      height: 40px;
      background: #e3f2fd;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .user-details {
      .user-name {
        font-size: 16px;
        font-weight: 600;
        color: #212529;
        margin: 0;
      }

      .user-email {
        font-size: 14px;
        color: #6c757d;
        margin: 0;
      }
    }
  }
}

.chat-messages {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.messages-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.message-item {
  display: flex;
  flex-direction: column;

  &.own-message {
    align-items: flex-end;

    .message-content {
      background: #007bff;
      color: white;
      border-radius: 18px 18px 4px 18px;
      max-width: 70%;
    }
  }

  &.user-message {
    align-items: flex-start;

    .message-content {
      background: #f1f3f4;
      color: #333;
      border-radius: 18px 18px 18px 4px;
      max-width: 70%;
    }
  }
}

.message-content {
  padding: 12px 16px;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-text {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.message-meta {
  font-size: 12px;
  opacity: 0.8;
  display: flex;
  align-items: center;
  gap: 4px;

  .message-time {
    font-weight: 500;
  }

  .sender-name {
    font-style: italic;
  }
}

.message-input-container {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;

  .selected-file-preview {
    margin-bottom: 12px;

    .file-preview {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 8px 12px;

      .file-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .file-name {
          font-size: 14px;
          font-weight: 500;
        }

        .file-size {
          font-size: 12px;
          color: #6c757d;
        }
      }

      .remove-file {
        background: none;
        border: none;
        color: #dc3545;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;

        &:hover {
          background: rgba(220, 53, 69, 0.1);
        }
      }
    }
  }
}

.message-input {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 24px;
  padding: 8px 12px;

  .attach-button {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 6px;
    border-radius: 50%;
    transition: all 0.2s;

    &:hover:not(:disabled) {
      background: #f8f9fa;
      color: #007bff;
    }

    &:disabled {
      color: #adb5bd;
      cursor: not-allowed;
    }
  }

  .input-field {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.4;
    padding: 8px 0;
    max-height: 120px;
    min-height: 20px;
  }

  .send-button {
    width: 36px;
    height: 36px;
    border: none;
    background: #007bff;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;

    &:hover:not(:disabled) {
      background: #0056b3;
      transform: scale(1.05);
    }

    &:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
    }
  }
}

/* Scrollbar styling */
.messages-list::-webkit-scrollbar {
  width: 6px;
}

.messages-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Load more and message type styling
.load-more-indicator, .no-more-messages {
  text-align: center;
  padding: 12px;
  color: #6c757d;
  font-size: 12px;

  fa-icon {
    margin-right: 8px;
  }
}

.no-more-messages {
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 12px;
}

// Message types styling
.message-image {
  img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.02);
    }
  }

  .message-text {
    margin-top: 8px;
  }
}

.message-file {
  .file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.2s;

    &:hover {
      background: rgba(0, 0, 0, 0.1);
    }

    .file-details {
      flex: 1;

      .file-name {
        font-weight: 500;
        font-size: 14px;
        margin-bottom: 2px;
      }

      .file-size {
        font-size: 12px;
        color: #6c757d;
      }
    }
  }

  .message-text {
    margin-top: 8px;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .support-chat-container {
    height: 500px;
  }

  .chat-header {
    padding: 12px 16px;
  }

  .messages-list {
    padding: 12px;
  }

  .message-input-container {
    padding: 12px 16px;
  }

  .message-item {
    &.own-message .message-content,
    &.user-message .message-content {
      max-width: 85%;
    }
  }
}
