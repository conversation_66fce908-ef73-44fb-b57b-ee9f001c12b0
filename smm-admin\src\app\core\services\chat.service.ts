import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { ConfigService } from './config.service';
import { UserService } from './user.service';
import { Client } from '@stomp/stompjs';
import SockJS from 'sockjs-client';

export interface ChatRoom {
  id: number;
  name: string;
  type: 'DIRECT' | 'GROUP' | 'SUPPORT';
  created_by: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  creator?: any;
  participants?: ChatParticipant[];
  last_message?: ChatMessage;
  unread_count?: number;
}

export interface ChatMessage {
  id: number;
  chat_room_id?: number;

  sender_id?: number;

  content: string;
  message_type?: 'TEXT' | 'IMAGE' | 'FILE';

  file_url?: string;

  file_name?: string;

  file_size?: number;

  created_at?: string;

  updated_at?: string;

  sender?: any;
}

export interface ChatParticipant {
  id: number;
  chat_room_id: number;
  user_id: number;
  joined_at: string;
  last_read_at?: string;
  is_active: boolean;
  user?: any;
}

export interface ChatUser {
  id: number;
  user_name: string;
  full_name: string;
  email: string;
  avatar?: string;
  roles: string[];
  last_login_at?: string;
  is_online: boolean;
}

export interface ChatRoomRequest {
  name?: string;
  type: 'DIRECT' | 'GROUP' | 'SUPPORT';
  participant_user_ids: number[];
}

export interface ChatMessageRequest {
  chat_room_id?: number;

  content: string;
  message_type?: 'TEXT' | 'IMAGE' | 'FILE';

  file_url?: string;

  file_name?: string;

  file_size?: number;
}

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private stompClient: Client | null = null;
  private messagesSubject = new BehaviorSubject<ChatMessage[]>([]);
  private chatRoomsSubject = new BehaviorSubject<ChatRoom[]>([]);
  private onlineUsersSubject = new BehaviorSubject<number[]>([]);
  private supportMessagesSubject = new BehaviorSubject<ChatMessage[]>([]);
  private roomMessagesMap = new Map<number, BehaviorSubject<ChatMessage[]>>();

  public messages$ = this.messagesSubject.asObservable();
  public chatRooms$ = this.chatRoomsSubject.asObservable();
  public onlineUsers$ = this.onlineUsersSubject.asObservable();
  public supportMessages$ = this.supportMessagesSubject.asObservable();

  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    private userService: UserService
  ) {
    this.initializeWebSocketConnection();
  }

  private initializeWebSocketConnection(): void {
    try {
      // Get current user and token
      const currentUser = this.userService.userValue();
      const token = currentUser?.tokens?.access_token;

      console.log('=== WebSocket Initialization ===');
      console.log('API URL:', this.configService.apiUrl);
      console.log('WebSocket URL:', `${this.configService.apiUrl}/ws`);
      console.log('Token present:', token ? 'Yes' : 'No');
      console.log('Client ID present:', currentUser?.tokens?.client_id ? 'Yes' : 'No');
      console.log('User ID:', currentUser?.id);
      console.log('User info:', currentUser);

      // Try direct connection to backend for testing
      const directBackendUrl = 'http://192.168.1.2:8095/api/v1/ws';
      console.log('Direct backend WebSocket URL:', directBackendUrl);

      // Use proxy URL for now, but log both options
      const wsUrl = `${this.configService.apiUrl}/ws`;
      console.log('Using WebSocket URL:', wsUrl);

      // TEMPORARY: Try direct connection for testing
      const socket = new SockJS(directBackendUrl);
      // const socket = new SockJS(wsUrl);
      this.stompClient = new Client({
        webSocketFactory: () => socket,
        debug: (str) => {
          console.log('STOMP: ' + str);
        },
        reconnectDelay: 5000,
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
        connectHeaders: token ? {
          'Authorization': `Bearer ${token}`,
          'x-client-id': `${currentUser?.tokens?.client_id}`
        } : {},
      });

    this.stompClient.onConnect = (frame) => {
      console.log('=== WebSocket Connected Successfully ===');
      console.log('Connection frame:', frame);
      console.log('User ID:', currentUser?.id);
      console.log('User info:', currentUser);
      console.log('Subscribing to WebSocket channels...');

      // Subscribe to personal message queue
      const personalSub = this.stompClient?.subscribe('/user/queue/messages', (message) => {
        console.log('=== Received personal message via WebSocket ===');
        console.log('Message headers:', message.headers);
        console.log('Message body:', message.body);
        try {
          const chatMessage: ChatMessage = JSON.parse(message.body);
          console.log('Parsed message:', chatMessage);
          this.handleNewMessage(chatMessage);
        } catch (error) {
          console.error('Error parsing personal message:', error);
        }
      });
      console.log('Personal messages subscription:', personalSub);

      // Subscribe to support messages queue
      const supportSub = this.stompClient?.subscribe('/user/queue/support-messages', (message) => {
        console.log('=== Received support message via WebSocket ===');
        console.log('Message headers:', message.headers);
        console.log('Message body:', message.body);
        try {
          const chatMessage: ChatMessage = JSON.parse(message.body);
          console.log('Parsed support message:', chatMessage);
          this.handleSupportMessage(chatMessage);
        } catch (error) {
          console.error('Error parsing support message:', error);
        }
      });
      console.log('Support messages subscription result:', supportSub);
      console.log('Support messages subscription ID:', supportSub?.id);
      console.log('Support messages subscription unsubscribe function:', typeof supportSub?.unsubscribe);

      // Subscribe to public chat updates
      const publicSub = this.stompClient?.subscribe('/topic/public', (message) => {
        console.log('=== Received public message via WebSocket ===');
        console.log('Message headers:', message.headers);
        console.log('Message body:', message.body);
        try {
          const chatMessage: ChatMessage = JSON.parse(message.body);
          console.log('Parsed public message:', chatMessage);
          this.handleNewMessage(chatMessage);
        } catch (error) {
          console.error('Error parsing public message:', error);
        }
      });
      console.log('Public messages subscription:', publicSub);

      console.log('=== All WebSocket subscriptions completed ===');
    };

      this.stompClient.onStompError = (frame) => {
        console.error('=== WebSocket STOMP Error ===');
        console.error('Error message:', frame.headers['message']);
        console.error('Error details:', frame.body);
        console.error('Full frame:', frame);
      };

      this.stompClient.onWebSocketError = (event) => {
        console.error('=== WebSocket Error ===');
        console.error('WebSocket error event:', event);
      };

      this.stompClient.onWebSocketClose = (event) => {
        console.warn('=== WebSocket Closed ===');
        console.warn('Close event:', event);
      };

      console.log('Activating WebSocket connection...');
      this.stompClient.activate();
    } catch (error) {
      console.error('Failed to initialize WebSocket connection:', error);
    }
  }

  private handleNewMessage(message: ChatMessage): void {
    console.log('Handling new message:', message);

    // Add to general messages
    const currentMessages = this.messagesSubject.value;
    this.messagesSubject.next([...currentMessages, message]);

    // Handle support messages (messages without specific room or support type)
    const roomId = message.chat_room_id;
    if (!roomId || this.isSupportMessage(message)) {
      const currentSupportMessages = this.supportMessagesSubject.value;
      // Check if message already exists to avoid duplicates
      const messageExists = currentSupportMessages.some(m =>
        (m.id && m.id === message.id) ||
        (m.content === message.content &&
         Math.abs(new Date(m.created_at || '').getTime() -
                  new Date(message.created_at || '').getTime()) < 1000)
      );

      if (!messageExists) {
        // Add new message at the end (newest messages go to the end in chat display)
        const updatedSupportMessages = [...currentSupportMessages, message];
        this.supportMessagesSubject.next(updatedSupportMessages);
      }
    }

    // Handle room-specific messages
    if (roomId) {
      if (!this.roomMessagesMap.has(roomId)) {
        this.roomMessagesMap.set(roomId, new BehaviorSubject<ChatMessage[]>([]));
      }

      const roomMessagesSubject = this.roomMessagesMap.get(roomId)!;
      const currentRoomMessages = roomMessagesSubject.value;

      // Check if message already exists to avoid duplicates
      const messageExists = currentRoomMessages.some(m =>
        (m.id && m.id === message.id) ||
        (m.content === message.content &&
         Math.abs(new Date(m.created_at || '').getTime() -
                  new Date(message.created_at || '').getTime()) < 1000)
      );

      if (!messageExists) {
        // Add new message at the end (newest messages go to the end in chat display)
        const updatedRoomMessages = [...currentRoomMessages, message];
        roomMessagesSubject.next(updatedRoomMessages);
      }
    }

    // Update chat rooms to reflect new message
    this.refreshChatRooms();
  }

  private handleSupportMessage(message: ChatMessage): void {
    console.log('Handling support message:', message);

    // Add to general messages
    const currentMessages = this.messagesSubject.value;
    this.messagesSubject.next([...currentMessages, message]);

    // Always add support messages to supportMessagesSubject regardless of room ID
    // because support messages can have room IDs but still need to appear in support chat
    const currentSupportMessages = this.supportMessagesSubject.value;
    console.log('Current support messages count:', currentSupportMessages.length);

    // Check if message already exists to avoid duplicates
    const messageExists = currentSupportMessages.some(m =>
      (m.id && m.id === message.id) ||
      (m.content === message.content &&
       Math.abs(new Date(m.created_at || '').getTime() -
                new Date(message.created_at || '').getTime()) < 1000)
    );

    console.log('Message exists check:', messageExists);

    if (!messageExists) {
      // Add new message at the end (newest messages go to the end in chat display)
      const updatedSupportMessages = [...currentSupportMessages, message];
      console.log('Updated support messages count:', updatedSupportMessages.length);
      this.supportMessagesSubject.next(updatedSupportMessages);
      console.log('Support messages observable updated!');
    }

    // Handle room-specific messages if room ID is present
    const roomId = message.chat_room_id;
    if (roomId) {
      if (!this.roomMessagesMap.has(roomId)) {
        this.roomMessagesMap.set(roomId, new BehaviorSubject<ChatMessage[]>([]));
      }

      const roomMessagesSubject = this.roomMessagesMap.get(roomId)!;
      const currentRoomMessages = roomMessagesSubject.value;

      // Check if message already exists to avoid duplicates
      const roomMessageExists = currentRoomMessages.some(m =>
        (m.id && m.id === message.id) ||
        (m.content === message.content &&
         Math.abs(new Date(m.created_at || '').getTime() -
                  new Date(message.created_at || '').getTime()) < 1000)
      );

      if (!roomMessageExists) {
        // Add new message at the end (newest messages go to the end in chat display)
        const updatedRoomMessages = [...currentRoomMessages, message];
        roomMessagesSubject.next(updatedRoomMessages);
      }
    }

    // Update chat rooms to reflect new message
    this.refreshChatRooms();
  }

  private isSupportMessage(message: ChatMessage): boolean {
    // Support messages are those received via /queue/support-messages
    // or messages without a specific room ID (legacy support messages)
    return !message.chat_room_id;
  }

  // Chat Rooms API
  getChatRooms(page: number = 0, size: number = 20): Observable<any> {
    return this.http.get(`${this.configService.apiUrl}/chat/rooms?page=${page}&size=${size}`);
  }

  createChatRoom(request: ChatRoomRequest): Observable<any> {
    return this.http.post<any>(`${this.configService.apiUrl}/chat/rooms`, request);
  }

  getOrCreateDirectChat(userId: number): Observable<any> {
    return this.http.post<any>(`${this.configService.apiUrl}/chat/rooms/direct/${userId}`, {});
  }

  getChatRoom(roomId: number): Observable<any> {
    return this.http.get<any>(`${this.configService.apiUrl}/chat/rooms/${roomId}`);
  }

  deleteChatRoom(roomId: number): Observable<void> {
    return this.http.delete<void>(`${this.configService.apiUrl}/chat/rooms/${roomId}`);
  }

  // Messages API
  sendMessage(request: ChatMessageRequest): Observable<any> {
    return this.http.post<any>(`${this.configService.apiUrl}/chat/messages`, request);
  }

  getMessages(roomId: number, page: number = 0, size: number = 50): Observable<any> {
    return this.http.get(`${this.configService.apiUrl}/chat/rooms/${roomId}/messages?page=${page}&size=${size}`);
  }

  markAsRead(roomId: number): Observable<void> {
    return this.http.post<void>(`${this.configService.apiUrl}/chat/rooms/${roomId}/read`, {});
  }

  // Users API
  getAvailableUsers(): Observable<any> {
    return this.http.get<any>(`${this.configService.apiUrl}/chat/users`);
  }

  // Support Chat API
  getSupportMessages(): Observable<any> {
    console.log('ChatService: Getting support messages from:', `${this.configService.apiUrl}/chat/support/messages`);
    return this.http.get<any>(`${this.configService.apiUrl}/chat/support/messages`);
  }

  sendSupportMessage(request: ChatMessageRequest): Observable<any> {
    console.log('ChatService: Sending support message:', request);
    console.log('ChatService: To URL:', `${this.configService.apiUrl}/chat/support/messages`);
    return this.http.post<any>(`${this.configService.apiUrl}/chat/support/messages`, request);
  }

  // New Support Chat APIs for Admin Panel
  getSupportChatRooms(page: number = 0, size: number = 20): Observable<any> {
    return this.http.get<any>(`${this.configService.apiUrl}/chat/support/rooms?page=${page}&size=${size}`);
  }

  getSupportChatMessages(userId: number, page: number = 0, size: number = 50): Observable<any> {
    return this.http.get<any>(`${this.configService.apiUrl}/chat/support/users/${userId}/messages?page=${page}&size=${size}`);
  }

  sendSupportReply(userId: number, request: ChatMessageRequest): Observable<any> {
    return this.http.post<any>(`${this.configService.apiUrl}/chat/support/users/${userId}/reply`, request);
  }

  getOrCreateSupportChatRoom(): Observable<any> {
    return this.http.get<any>(`${this.configService.apiUrl}/chat/support/room`);
  }

  // WebSocket methods
  sendMessageViaWebSocket(message: ChatMessageRequest): void {
    if (this.stompClient && this.stompClient.connected) {
      this.stompClient.publish({
        destination: '/app/chat.sendMessage',
        body: JSON.stringify(message)
      });
    }
  }

  disconnect(): void {
    if (this.stompClient) {
      this.stompClient.deactivate();
    }
  }

  // Helper methods
  private refreshChatRooms(): void {
    this.getChatRooms().subscribe(response => {
     
        this.chatRoomsSubject.next(response.content);
      
    });
  }

  // Get current messages for a specific room
  getMessagesForRoom(roomId: number): ChatMessage[] {
    return this.messagesSubject.value.filter(msg => msg.chat_room_id === roomId);
  }

  // Get observable for room messages
  getRoomMessages$(roomId: number): Observable<ChatMessage[]> {
    if (!this.roomMessagesMap.has(roomId)) {
      this.roomMessagesMap.set(roomId, new BehaviorSubject<ChatMessage[]>([]));
    }
    return this.roomMessagesMap.get(roomId)!.asObservable();
  }

  // Load messages for a specific room and update the observable
  loadRoomMessages(roomId: number, page: number = 0, size: number = 50): Observable<any> {
    return new Observable(observer => {
      this.getMessages(roomId, page, size).subscribe({
        next: (response) => {
          if (response.content) {
            // Backend already sorts DESC (newest first), so we reverse to show oldest first in chat
            const messages = response.content.reverse();

            if (!this.roomMessagesMap.has(roomId)) {
              this.roomMessagesMap.set(roomId, new BehaviorSubject<ChatMessage[]>([]));
            }

            if (page === 0) {
              // First page - replace all messages
              this.roomMessagesMap.get(roomId)!.next(messages);
            } else {
              // Subsequent pages - prepend older messages
              const currentMessages = this.roomMessagesMap.get(roomId)!.value;
              const combinedMessages = [...messages, ...currentMessages];
              this.roomMessagesMap.get(roomId)!.next(combinedMessages);
            }
          }
          observer.next(response);
          observer.complete();
        },
        error: (error) => {
          observer.error(error);
        }
      });
    });
  }

  // Load support messages and update the observable
  loadSupportMessages(): Observable<any> {
    return new Observable(observer => {
      this.getSupportMessages().subscribe({
        next: (response) => {
          if (response.content) {
            // Backend already sorts DESC (newest first), so we reverse to show oldest first in chat
            const messages = response.content.reverse();
            this.supportMessagesSubject.next(messages);
          } else if (response.content) {
            // Handle case where response.content is directly the array
            const messages = response.content.reverse();
            this.supportMessagesSubject.next(messages);
          }
          observer.next(response);
          observer.complete();
        },
        error: (error) => {
          observer.error(error);
        }
      });
    });
  }

  // Clear messages (useful when switching rooms)
  clearMessages(): void {
    this.messagesSubject.next([]);
  }

  // Clear room messages
  clearRoomMessages(roomId: number): void {
    if (this.roomMessagesMap.has(roomId)) {
      this.roomMessagesMap.get(roomId)!.next([]);
    }
  }

  // Clear support messages
  clearSupportMessages(): void {
    this.supportMessagesSubject.next([]);
  }

  // Check WebSocket connection status
  isWebSocketConnected(): boolean {
    return this.stompClient?.connected || false;
  }

  // Get WebSocket connection info for debugging
  getWebSocketInfo(): any {
    return {
      connected: this.stompClient?.connected || false,
      state: this.stompClient?.state || 'UNKNOWN',
      url: `${this.configService.apiUrl}/ws`,
      hasClient: !!this.stompClient
    };
  }

  // Force reconnect WebSocket
  reconnectWebSocket(): void {
    console.log('=== Force reconnecting WebSocket ===');
    if (this.stompClient) {
      this.stompClient.deactivate();
    }
    setTimeout(() => {
      this.initializeWebSocketConnection();
    }, 1000);
  }

  // Load more messages for pagination (when scrolling up)
  loadMoreRoomMessages(roomId: number, currentPage: number, size: number = 50): Observable<any> {
    console.log(`Loading more messages for room ${roomId}, page ${currentPage + 1}`);
    return this.loadRoomMessages(roomId, currentPage + 1, size);
  }

  // Get current page info for a room
  getRoomMessagesPagination(roomId: number): { currentPage: number, hasMore: boolean } {
    // This would need to be tracked separately, for now return defaults
    return { currentPage: 0, hasMore: true };
  }
}
