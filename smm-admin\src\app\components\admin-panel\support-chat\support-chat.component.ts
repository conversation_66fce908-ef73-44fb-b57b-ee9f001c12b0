import { Component, On<PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef, Input, Output, EventEmitter } from '@angular/core';
import { ChatService, ChatRoom, ChatMessage, ChatMessageRequest } from '../../../core/services/chat.service';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';

import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { IconName } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-support-chat',
  standalone: true,
  imports: [CommonModule, TranslateModule, FormsModule, IconsModule],
  templateUrl: './support-chat.component.html',
  styleUrls: ['./support-chat.component.scss']
})
export class SupportChatComponent implements OnInit, OnDestroy {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;
  @ViewChild('fileInput') fileInput!: ElementRef;
  @Input() selectedChatRoom: ChatRoom | null = null;
  @Output() backToRooms = new EventEmitter<void>();

  // Chat properties
  messages: ChatMessage[] = [];
  newMessage: string = '';
  isLoading = false;
  currentUser: UserRes | undefined;

  // Pagination properties
  currentPage = 0;
  hasMoreMessages = true;
  isLoadingMore = false;

  // File upload properties
  selectedFile: File | null = null;
  isUploadingFile = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private chatService: ChatService,
    private userService: UserService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    if (this.selectedChatRoom) {
      this.subscribeToRoomMessages();
      this.loadInitialMessages();
    }

    // Debug WebSocket connection
    this.debugWebSocketConnection();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadCurrentUser(): void {
    const userSub = this.userService.user$.subscribe(user => {
      this.currentUser = user;
    });
    this.subscriptions.push(userSub);
  }

  private subscribeToRoomMessages(): void {
    if (!this.selectedChatRoom) return;

    // Subscribe to room-specific messages observable for real-time updates
    const roomMessagesSub = this.chatService.getRoomMessages$(this.selectedChatRoom.id).subscribe(messages => {
      this.messages = messages;
      this.scrollToBottom();
    });
    this.subscriptions.push(roomMessagesSub);
  }

  private loadInitialMessages(): void {
    if (!this.selectedChatRoom) return;

    this.isLoading = true;
    this.chatService.loadRoomMessages(this.selectedChatRoom.id, 0, 50).subscribe({
      next: (response) => {
        console.log('Initial room messages loaded:', response);
        this.currentPage = 0;
        this.hasMoreMessages = response.content && response.content.length === 50;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading initial room messages:', error);
        this.isLoading = false;
      }
    });
  }

  loadChatMessages(): void {
    // This method is now mainly for manual refresh if needed
    // Real-time updates are handled by subscribeToRoomMessages()
    if (!this.selectedChatRoom) return;

    this.isLoading = true;
    this.chatService.loadRoomMessages(this.selectedChatRoom.id).subscribe({
      next: (response) => {
        console.log('Support chat messages refreshed:', response);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error refreshing support chat messages:', error);
        this.isLoading = false;
      }
    });
  }

  sendReply(): void {
    if (!this.newMessage.trim() && !this.selectedFile || !this.selectedChatRoom) {
      return;
    }

    console.log('=== Sending Support Reply ===');
    console.log('Message content:', this.newMessage.trim());
    console.log('Selected file:', this.selectedFile);

    this.isLoading = true;

    if (this.selectedFile) {
      this.sendFileReply();
    } else {
      this.sendTextReply();
    }
  }

  private sendTextReply(): void {
    if (!this.selectedChatRoom) return;

    const messageRequest: ChatMessageRequest = {
      content: this.newMessage.trim(),
      message_type: 'TEXT'
    };

    const sub = this.chatService.sendSupportReply(this.selectedChatRoom.created_by, messageRequest).subscribe({
      next: () => {
        this.newMessage = '';
        this.isLoading = false;
        // Force scroll to bottom when sending message
        this.forceScrollToBottom();
      },
      error: (error) => {
        console.error('Error sending support reply:', error);
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  private sendFileReply(): void {
    if (!this.selectedFile || !this.selectedChatRoom) return;

    this.isUploadingFile = true;

    // First upload the file
    this.chatService.uploadFile(this.selectedFile).subscribe({
      next: (uploadResponse: any) => {
        console.log('File uploaded successfully:', uploadResponse);

        // Then send reply with image info
        const messageRequest: ChatMessageRequest = {
          content: this.newMessage.trim() || 'Sent an image',
          message_type: 'IMAGE', // Only images allowed
          file_url: uploadResponse.url, // Use 'url' from response
          file_name: this.selectedFile!.name,
          file_size: this.selectedFile!.size
        };

        const sub = this.chatService.sendSupportReply(this.selectedChatRoom!.created_by, messageRequest).subscribe({
          next: () => {
            console.log('Image reply sent successfully');
            this.newMessage = '';
            this.selectedFile = null;
            this.isLoading = false;
            this.isUploadingFile = false;
            // Force scroll to bottom when sending image
            this.forceScrollToBottom();
          },
          error: (error: any) => {
            console.error('Error sending image reply:', error);
            this.isLoading = false;
            this.isUploadingFile = false;
          }
        });
        this.subscriptions.push(sub);
      },
      error: (error: any) => {
        console.error('Error uploading file:', error);
        this.isLoading = false;
        this.isUploadingFile = false;
      }
    });
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendReply();
    }
  }

  private scrollToBottom(force: boolean = false): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;

        // Check if user is near bottom (within 100px) or force scroll
        const isNearBottom = element.scrollHeight - element.scrollTop - element.clientHeight < 100;

        if (force || isNearBottom) {
          // Smooth scroll to bottom
          element.scrollTo({
            top: element.scrollHeight,
            behavior: 'smooth'
          });
        }
      }
    }, 100);
  }

  // Force scroll to bottom (used when sending messages)
  private forceScrollToBottom(): void {
    this.scrollToBottom(true);
  }

  formatMessageTime(timestamp: string): string {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString();
  }

  getCurrentUserId(): number {
    return this.currentUser?.id || 0;
  }

  onBackToRooms(): void {
    this.backToRooms.emit();
  }

  getUserDisplayName(): string {
    if (!this.selectedChatRoom?.creator) return 'Unknown User';
    return this.selectedChatRoom.creator.fullName || 
           this.selectedChatRoom.creator.full_name || 
           this.selectedChatRoom.creator.userName || 
           this.selectedChatRoom.creator.user_name || 
           'Unknown User';
  }

  getUserEmail(): string {
    if (!this.selectedChatRoom?.creator) return '';
    return this.selectedChatRoom.creator.email || '';
  }

  private debugWebSocketConnection(): void {
    console.log('=== Support Chat Component - WebSocket Debug ===');
    console.log('WebSocket Info:', this.chatService.getWebSocketInfo());
    console.log('Current User:', this.currentUser);
    console.log('Selected Chat Room:', this.selectedChatRoom);

    // Check connection status every 5 seconds
    setInterval(() => {
      const wsInfo = this.chatService.getWebSocketInfo();
      if (!wsInfo.connected) {
        console.warn('WebSocket not connected:', wsInfo);
      }
    }, 5000);
  }

  // Method to manually test WebSocket
  testWebSocketConnection(): void {
    console.log('=== Testing WebSocket Connection ===');
    const wsInfo = this.chatService.getWebSocketInfo();
    console.log('WebSocket Info:', wsInfo);

    if (!wsInfo.connected) {
      console.log('WebSocket not connected, attempting reconnection...');
      this.chatService.reconnectWebSocket();
    } else {
      console.log('WebSocket is connected successfully!');

      // Test subscription by checking if we can receive messages
      console.log('Testing message reception...');
      console.log('Current support messages count:', this.messages.length);
      console.log('Support messages observable:', this.chatService.supportMessages$);

      // Force refresh messages to compare with WebSocket
      console.log('Manually refreshing messages for comparison...');
      this.loadChatMessages();
    }
  }

  // Force reconnect WebSocket for testing
  forceReconnectWebSocket(): void {
    console.log('=== Force Reconnecting WebSocket ===');
    this.chatService.forceReconnectWebSocket();

    // Wait a moment then reload messages
    setTimeout(() => {
      this.loadInitialMessages();
    }, 2000);
  }

  // Test WebSocket with detailed info
  testWebSocketDetailed(): void {
    console.log('=== Detailed WebSocket Test ===');
    this.chatService.testWebSocketConnection();
  }

  // File handling methods
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check file type - only images allowed
      if (!file.type.startsWith('image/')) {
        this.translateService.get('chat.only_images_allowed').subscribe(message => {
          alert(message || 'Only image files are allowed');
        });
        return;
      }

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        this.translateService.get('chat.file_too_large').subscribe(message => {
          alert(message || 'File size must be less than 10MB');
        });
        return;
      }

      this.selectedFile = file;
      console.log('Image selected:', file);
    }
  }

  removeSelectedFile(): void {
    this.selectedFile = null;
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  // Scroll handling for load more
  onScroll(event: any): void {
    const element = event.target;
    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {
      this.loadMoreMessages();
    }
  }

  private loadMoreMessages(): void {
    if (!this.selectedChatRoom || this.isLoadingMore || !this.hasMoreMessages) return;

    this.isLoadingMore = true;
    const nextPage = this.currentPage + 1;

    this.chatService.loadRoomMessages(this.selectedChatRoom.id, nextPage, 50).subscribe({
      next: (response) => {
        console.log(`Loaded page ${nextPage} messages:`, response);

        if (response.content && response.content.length > 0) {
          this.currentPage = nextPage;
          this.hasMoreMessages = response.content.length === 50;
        } else {
          this.hasMoreMessages = false;
        }

        this.isLoadingMore = false;
      },
      error: (error) => {
        console.error('Error loading more messages:', error);
        this.isLoadingMore = false;
      }
    });
  }

  // Message type checking
  isImageMessage(message: ChatMessage): boolean {
    return message.message_type === 'IMAGE' && !!message.file_url;
  }

  isFileMessage(message: ChatMessage): boolean {
    return message.message_type === 'FILE' && !!message.file_url;
  }

  getFileIcon(fileName: string): IconName {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return 'file-pdf';
      case 'doc':
      case 'docx': return 'file-word';
      case 'xls':
      case 'xlsx': return 'file-excel';
      case 'ppt':
      case 'pptx': return 'file-powerpoint';
      case 'txt': return 'file-alt';
      default: return 'file';
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  downloadFile(fileUrl: string, fileName: string): void {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  // Get image preview URL for selected file
  getImagePreview(file: File): string {
    return URL.createObjectURL(file);
  }
}
