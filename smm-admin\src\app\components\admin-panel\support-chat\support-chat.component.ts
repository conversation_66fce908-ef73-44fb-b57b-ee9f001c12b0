import { Component, On<PERSON>nit, On<PERSON><PERSON>roy, ViewChild, ElementRef, Input, Output, EventEmitter } from '@angular/core';
import { ChatService, ChatRoom, ChatMessage, ChatMessageRequest } from '../../../core/services/chat.service';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';

import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-support-chat',
  standalone: true,
  imports: [CommonModule, TranslateModule, FormsModule, IconsModule],
  templateUrl: './support-chat.component.html',
  styleUrls: ['./support-chat.component.scss']
})
export class SupportChatComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;
  @Input() selectedChatRoom: ChatRoom | null = null;
  @Output() backToRooms = new EventEmitter<void>();

  // Chat properties
  messages: ChatMessage[] = [];
  newMessage: string = '';
  isLoading = false;
  currentUser: UserRes | undefined;

  private subscriptions: Subscription[] = [];

  constructor(
    private chatService: ChatService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    if (this.selectedChatRoom) {
      this.subscribeToRoomMessages();
      this.loadInitialMessages();
    }

    // Debug WebSocket connection
    this.debugWebSocketConnection();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadCurrentUser(): void {
    const userSub = this.userService.user$.subscribe(user => {
      this.currentUser = user;
    });
    this.subscriptions.push(userSub);
  }

  private subscribeToRoomMessages(): void {
    if (!this.selectedChatRoom) return;

    // Subscribe to room-specific messages observable for real-time updates
    const roomMessagesSub = this.chatService.getRoomMessages$(this.selectedChatRoom.id).subscribe(messages => {
      this.messages = messages;
      this.scrollToBottom();
    });
    this.subscriptions.push(roomMessagesSub);
  }

  private loadInitialMessages(): void {
    if (!this.selectedChatRoom) return;

    this.isLoading = true;
    this.chatService.loadRoomMessages(this.selectedChatRoom.id).subscribe({
      next: (response) => {
        console.log('Initial room messages loaded:', response);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading initial room messages:', error);
        this.isLoading = false;
      }
    });
  }

  loadChatMessages(): void {
    // This method is now mainly for manual refresh if needed
    // Real-time updates are handled by subscribeToRoomMessages()
    if (!this.selectedChatRoom) return;

    this.isLoading = true;
    this.chatService.loadRoomMessages(this.selectedChatRoom.id).subscribe({
      next: (response) => {
        console.log('Support chat messages refreshed:', response);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error refreshing support chat messages:', error);
        this.isLoading = false;
      }
    });
  }

  sendReply(): void {
    if (!this.newMessage.trim() || !this.selectedChatRoom) {
      return;
    }

    this.isLoading = true;
    const messageRequest: ChatMessageRequest = {
      content: this.newMessage.trim(),
      message_type: 'TEXT'
    };

    const sub = this.chatService.sendSupportReply(this.selectedChatRoom.created_by, messageRequest).subscribe({
      next: (response) => {
        this.newMessage = '';
        // No need to reload messages - WebSocket will handle real-time updates
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error sending support reply:', error);
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendReply();
    }
  }

  private scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  formatMessageTime(timestamp: string): string {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString();
  }

  getCurrentUserId(): number {
    return this.currentUser?.id || 0;
  }

  onBackToRooms(): void {
    this.backToRooms.emit();
  }

  getUserDisplayName(): string {
    if (!this.selectedChatRoom?.creator) return 'Unknown User';
    return this.selectedChatRoom.creator.fullName || 
           this.selectedChatRoom.creator.full_name || 
           this.selectedChatRoom.creator.userName || 
           this.selectedChatRoom.creator.user_name || 
           'Unknown User';
  }

  getUserEmail(): string {
    if (!this.selectedChatRoom?.creator) return '';
    return this.selectedChatRoom.creator.email || '';
  }

  private debugWebSocketConnection(): void {
    console.log('=== Support Chat Component - WebSocket Debug ===');
    console.log('WebSocket Info:', this.chatService.getWebSocketInfo());
    console.log('Current User:', this.currentUser);
    console.log('Selected Chat Room:', this.selectedChatRoom);

    // Check connection status every 5 seconds
    setInterval(() => {
      const wsInfo = this.chatService.getWebSocketInfo();
      if (!wsInfo.connected) {
        console.warn('WebSocket not connected:', wsInfo);
      }
    }, 5000);
  }

  // Method to manually test WebSocket
  testWebSocketConnection(): void {
    console.log('=== Testing WebSocket Connection ===');
    const wsInfo = this.chatService.getWebSocketInfo();
    console.log('WebSocket Info:', wsInfo);

    if (!wsInfo.connected) {
      console.log('WebSocket not connected, attempting reconnection...');
      this.chatService.reconnectWebSocket();
    } else {
      console.log('WebSocket is connected successfully!');

      // Test subscription by checking if we can receive messages
      console.log('Testing message reception...');
      console.log('Current support messages count:', this.messages.length);
      console.log('Support messages observable:', this.chatService.supportMessages$);

      // Force refresh messages to compare with WebSocket
      console.log('Manually refreshing messages for comparison...');
      this.loadChatMessages();
    }
  }
}
