<!-- Chat Toggle Button -->
<div class="chat-toggle" [class.active]="isChatOpen" (click)="toggleChat()">
  <fa-icon [icon]="['fas', 'comments']"></fa-icon>
  <span class="unread-badge" *ngIf="getTotalUnreadCount() > 0">{{ getTotalUnreadCount() }}</span>
</div>

<!-- Simple Chat Window -->
<div class="chat-window" [class.open]="isChatOpen">
  <div class="chat-header">
    <h4>{{ 'chat.support_chat' | translate }}</h4>
    <div class="chat-actions">
      <button class="btn-icon" (click)="toggleChat()" title="Close">
       <fa-icon [icon]="['fas', 'times']"></fa-icon>
      </button>
    </div>
  </div>

  <div class="chat-body">
    <!-- Messages Display -->
    <div class="chat-messages" #messagesContainer>
      <div class="messages-list">
        <div *ngFor="let message of supportMessages"
             class="message-item"
             [class.own-message]="(message.sender_id) === currentUser?.id"
             [class.admin-message]="(message.sender_id) !== currentUser?.id">
          <div class="message-content">
            <div class="message-text">{{ message.content }}</div>
            <div class="message-time">
              {{ formatMessageTime(message.created_at || '') }}
              <span *ngIf="message.sender?.full_name || message.sender?.user_name" class="sender-name">
                - {{ message.sender?.full_name || message.sender?.user_name }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div class="empty-state" *ngIf="supportMessages.length === 0">
        <fa-icon [icon]="['fas', 'comments']"></fa-icon>
        <h5>{{ 'chat.no_messages' | translate }}</h5>
        <p>{{ 'chat.start_conversation' | translate }}</p>
      </div>
    </div>

    <!-- Message Input -->
    <div class="message-input-container">
      <div class="message-input">
        <textarea #messageInput
                  [(ngModel)]="newMessage"
                  (keydown)="onKeyPress($event)"
                  [placeholder]="'chat.type_support_message' | translate"
                  rows="1"></textarea>
        <button class="send-button"
                (click)="sendSupportMessage()"
                [disabled]="!newMessage.trim() || isLoading">
          <fa-icon [icon]="['fas', 'paper-plane']" *ngIf="!isLoading"></fa-icon>
          <fa-icon [icon]="['fas', 'spinner']" [spin]="true" *ngIf="isLoading"></fa-icon>
        </button>
      </div>
    </div>
  </div>
</div>


