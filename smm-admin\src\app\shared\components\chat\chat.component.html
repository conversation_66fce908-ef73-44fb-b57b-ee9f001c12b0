<!-- Chat Toggle Button -->
<div class="chat-toggle" [class.active]="isChatOpen" (click)="toggleChat()">
  <fa-icon [icon]="['fas', 'comments']"></fa-icon>
  <span class="unread-badge" *ngIf="unreadCount > 0">{{ unreadCount }}</span>
  <div class="notification-dot" *ngIf="hasNewMessages && !isChatOpen"></div>
</div>

<!-- Simple Chat Window -->
<div class="chat-window" [class.open]="isChatOpen">
  <div class="chat-header">
    <h4>{{ 'chat.support_chat' | translate }}</h4>
    <div class="chat-actions">
      <!-- Debug buttons (remove in production) -->
      <button class="btn-debug" (click)="testWebSocketConnection()" title="Test WebSocket">
        <fa-icon [icon]="['fas', 'wifi']"></fa-icon>
      </button>
      <button class="btn-debug" (click)="forceReconnectWebSocket()" title="Force Reconnect">
        <fa-icon [icon]="['fas', 'sync']"></fa-icon>
      </button>
      <button class="btn-debug" (click)="testRoomSubscription()" title="Test Room">
        <fa-icon [icon]="['fas', 'cog']"></fa-icon>
      </button>

      <button class="btn-icon" (click)="toggleChat()" title="Close">
       <fa-icon [icon]="['fas', 'times']"></fa-icon>
      </button>
    </div>
  </div>

  <div class="chat-body">
    <!-- Messages Display -->
    <div class="chat-messages" #messagesContainer (scroll)="onScroll($event)">
      <!-- Load more indicator -->
      <div class="load-more-indicator" *ngIf="isLoadingMore">
        <fa-icon [icon]="['fas', 'spinner']" [spin]="true"></fa-icon>
        <span>{{ 'chat.loading_more' | translate }}</span>
      </div>

      <!-- No more messages indicator -->
      <div class="no-more-messages" *ngIf="!hasMoreMessages && supportMessages.length > 0">
        <span>{{ 'chat.no_more_messages' | translate }}</span>
      </div>

      <div class="messages-list">
        <div *ngFor="let message of supportMessages"
             class="message-item"
             [class.own-message]="(message.sender_id) === currentUser?.id"
             [class.admin-message]="(message.sender_id) !== currentUser?.id">
          <div class="message-content">
            <!-- Image message -->
            <div *ngIf="isImageMessage(message)" class="message-image">
              <img [src]="message.file_url" [alt]="message.file_name" (click)="downloadFile(message.file_url!, message.file_name!)">
              <div class="message-text" *ngIf="message.content && message.content.trim()">{{ message.content }}</div>
            </div>

            <!-- File message -->
            <div *ngIf="isFileMessage(message)" class="message-file">
              <div class="file-info" (click)="downloadFile(message.file_url!, message.file_name!)">
                <fa-icon [icon]="['fas', getFileIcon(message.file_name!)]"></fa-icon>
                <div class="file-details">
                  <div class="file-name">{{ message.file_name }}</div>
                  <div class="file-size">{{ formatFileSize(message.file_size!) }}</div>
                </div>
                <fa-icon [icon]="['fas', 'download']"></fa-icon>
              </div>
              <div class="message-text" *ngIf="message.content && message.content.trim()">{{ message.content }}</div>
            </div>

            <!-- Text message -->
            <div *ngIf="!isImageMessage(message) && !isFileMessage(message)" class="message-text">{{ message.content }}</div>

            <div class="message-time">
              {{ formatMessageTime(message.created_at || '') }}
              <span *ngIf="message.sender?.full_name || message.sender?.user_name" class="sender-name">
                - {{ message.sender?.full_name || message.sender?.user_name }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div class="empty-state" *ngIf="supportMessages.length === 0 && !isLoading">
        <fa-icon [icon]="['fas', 'comments']"></fa-icon>
        <h5>{{ 'chat.no_messages' | translate }}</h5>
        <p>{{ 'chat.start_conversation' | translate }}</p>
      </div>

      <!-- Loading state -->
      <div class="loading-state" *ngIf="isLoading && supportMessages.length === 0">
        <fa-icon [icon]="['fas', 'spinner']" [spin]="true"></fa-icon>
        <span>{{ 'chat.loading_messages' | translate }}</span>
      </div>

      <!-- New messages indicator -->
      <div class="new-messages-indicator" *ngIf="hasNewMessages && isUserScrolledUp" (click)="scrollToNewMessages()">
        <fa-icon [icon]="['fas', 'arrow-down']"></fa-icon>
        <span>{{ unreadCount }} new message{{ unreadCount > 1 ? 's' : '' }}</span>
      </div>
    </div>

    <!-- Message Input -->
    <div class="message-input-container">
      <!-- Selected file preview -->
      <div class="selected-file-preview" *ngIf="selectedFile">
        <div class="file-preview">
          <div class="image-preview" *ngIf="selectedFile.type.startsWith('image/')">
            <img [src]="getImagePreview(selectedFile)" [alt]="selectedFile.name" class="preview-image">
          </div>
          <div class="file-info">
            <fa-icon [icon]="['fas', 'image']"></fa-icon>
            <div class="file-details">
              <span class="file-name">{{ selectedFile.name }}</span>
              <span class="file-size">({{ formatFileSize(selectedFile.size) }})</span>
            </div>
          </div>
          <button class="remove-file" (click)="removeSelectedFile()" type="button">
            <fa-icon [icon]="['fas', 'times']"></fa-icon>
          </button>
        </div>
      </div>

      <div class="message-input">
        <button class="attach-button" (click)="triggerFileInput()" type="button" [disabled]="isLoading || isUploadingFile">
          <fa-icon [icon]="['fas', 'paperclip']"></fa-icon>
        </button>

        <textarea #messageInput
                  [(ngModel)]="newMessage"
                  (keydown)="onKeyPress($event)"
                  [placeholder]="'chat.type_support_message' | translate"
                  rows="1"></textarea>

        <button class="send-button"
                (click)="sendSupportMessage()"
                [disabled]="(!newMessage.trim() && !selectedFile) || isLoading || isUploadingFile">
          <fa-icon [icon]="['fas', 'paper-plane']" *ngIf="!isLoading && !isUploadingFile"></fa-icon>
          <fa-icon [icon]="['fas', 'spinner']" [spin]="true" *ngIf="isLoading || isUploadingFile"></fa-icon>
        </button>
      </div>

      <!-- Hidden file input -->
      <input #fileInput
             type="file"
             (change)="onFileSelected($event)"
             accept="image/*"
             style="display: none;">
    </div>
  </div>
</div>


